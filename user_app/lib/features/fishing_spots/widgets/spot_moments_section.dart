import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/fishing_spots/utils/moment_type_data_parser.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';

class SpotMomentsSection extends StatefulWidget {
  final int fishingSpotId;
  final String fishingSpotName;

  const SpotMomentsSection({
    super.key,
    required this.fishingSpotId,
    required this.fishingSpotName,
  });

  @override
  State<SpotMomentsSection> createState() => _SpotMomentsSectionState();
}

class _SpotMomentsSectionState extends State<SpotMomentsSection> {
  List<MomentVo> moments = [];
  bool isLoading = false;
  bool hasMore = true;
  int currentPage = 1;
  final int pageSize = 10;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    _loadMoments();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 400) {
      if (!isLoading && hasMore) {
        _loadMoreMoments();
      }
    }
  }

  Future<void> _loadMoments() async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
      currentPage = 1;
      moments.clear();
    });

    try {
      final momentService = Provider.of<MomentService>(context, listen: false);
      final response = await momentService.getMomentsByFishingSpot(
        widget.fishingSpotId,
        currentPage,
        pageSize,
      );

      setState(() {
        moments = response.records;
        hasMore = response.records.length >= pageSize;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载动态失败：$e')),
        );
      }
    }
  }

  Future<void> _loadMoreMoments() async {
    if (isLoading || !hasMore) return;

    setState(() {
      isLoading = true;
    });

    try {
      final momentService = Provider.of<MomentService>(context, listen: false);
      final response = await momentService.getMomentsByFishingSpot(
        widget.fishingSpotId,
        currentPage + 1,
        pageSize,
      );

      setState(() {
        moments.addAll(response.records);
        currentPage++;
        hasMore = response.records.length >= pageSize;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载更多动态失败：$e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Flat drag handle
          Center(
            child: Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 8, bottom: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          // Flat header design matching FishingSpotsPage style
          Container(
            color: Colors.white,
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
            child: Row(
              children: [
                // Simple icon container
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.timeline,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.fishingSpotName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${moments.length}条钓友动态',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Simple filter button
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.sort,
                        size: 16,
                        color: Colors.grey.shade700,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '最新',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Simple close button
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.close,
                      size: 20,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content area
          Expanded(
            child: _buildFlatContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildFlatContent() {
    if (isLoading && moments.isEmpty) {
      return Container(
        color: Colors.grey.shade50,
        child: const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    if (moments.isEmpty) {
      return Container(
        color: Colors.grey.shade50,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.article_outlined,
                  size: 32,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '暂无动态数据',
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '还没有人在这里分享动态',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  '成为第一个分享的人',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: Colors.grey.shade50,
      child: RefreshIndicator(
        onRefresh: _loadMoments,
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
          itemCount: moments.length +
              (isLoading && moments.isNotEmpty ? 1 : 0) +
              (!hasMore && moments.isNotEmpty ? 1 : 0),
          itemBuilder: (context, index) {
            // Loading indicator at bottom
            if (index == moments.length && isLoading) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              );
            }

            // End of list indicator
            if (index == moments.length && !hasMore) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Center(
                  child: Text(
                    '已加载全部',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 13,
                    ),
                  ),
                ),
              );
            }

            if (index >= moments.length) {
              return const SizedBox.shrink();
            }

            final moment = moments[index];
            return _buildFlatMomentCard(moment);
          },
        ),
      ),
    );
  }

  // 在 SpotMomentsSection 中替换 _buildFlatMomentCard 方法
  Widget _buildFlatMomentCard(MomentVo moment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToMomentDetail(moment),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 用户头部和动态类型标签合并在一行
                  Row(
                    children: [
                      // 用户头像
                      Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(18),
                        ),
                        child: moment.publisher?.avatarUrl != null
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(18),
                                child: CachedNetworkImage(
                                  imageUrl: moment.publisher!.avatarUrl!,
                                  fit: BoxFit.cover,
                                  errorWidget: (context, url, error) => Icon(
                                    Icons.person,
                                    size: 20,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              )
                            : Icon(
                                Icons.person,
                                size: 20,
                                color: Theme.of(context).primaryColor,
                              ),
                      ),
                      const SizedBox(width: 12),

                      // 用户信息
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              moment.publisher?.name ??
                                  moment.userName ??
                                  'Unknown User',
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                height: 1.2,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _formatFlatTime(
                                  moment.createTime ?? DateTime.now()),
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 动态类型标签（更简洁）
                      if (moment.momentType != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getMomentTypeColor(moment.momentType!)
                                .withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getMomentTypeIcon(moment.momentType!),
                                size: 14,
                                color: _getMomentTypeColor(moment.momentType!),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                MomentTypeDataParser.getMomentTypeDisplayName(
                                    moment.momentType),
                                style: TextStyle(
                                  color:
                                      _getMomentTypeColor(moment.momentType!),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),

                  // 类型特定数据展示（使用新的设计）
                  if (moment.momentType != null &&
                      moment.typeSpecificData != null) ...[
                    _buildTypeSpecificContent(
                        moment.momentType!, moment.typeSpecificData!),
                  ],

                  // 文本内容
                  if (moment.content != null && moment.content!.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Text(
                      moment.content!,
                      style: const TextStyle(
                        fontSize: 15,
                        height: 1.4,
                        color: Colors.black87,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),

            // 图片轮播（移到 padding 外面，更美观）
            if (moment.pictures != null && moment.pictures!.isNotEmpty)
              _buildImprovedImageCarousel(moment.pictures!),

            // 互动栏
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
              child: Row(
                children: [
                  _buildInteractionButton(
                    icon: (moment.isLiked ?? false)
                        ? Icons.thumb_up
                        : Icons.thumb_up_outlined,
                    count: moment.numberOfLikes,
                    color: (moment.isLiked ?? false)
                        ? Colors.blue.shade600
                        : Colors.grey.shade500,
                    onTap: () {
                      // TODO: 处理点赞
                    },
                  ),
                  const SizedBox(width: 20),
                  _buildInteractionButton(
                    icon: Icons.chat_bubble_outline,
                    count: (moment.commentCount ?? 0).toInt(),
                    color: Colors.grey.shade500,
                    onTap: () => _navigateToMomentDetail(moment),
                  ),
                  const Spacer(),
                  _buildInteractionButton(
                    icon: Icons.share_outlined,
                    count: null,
                    color: Colors.grey.shade500,
                    onTap: () {
                      // TODO: 处理分享
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

// 新增：可点击的互动按钮
  Widget _buildInteractionButton({
    required IconData icon,
    int? count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: color,
            ),
            if (count != null) ...[
              const SizedBox(width: 4),
              Text(
                count > 999 ? '999+' : count.toString(),
                style: TextStyle(
                  fontSize: 13,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

// 将原有的 _buildTypeSpecificContent 方法替换为改进版本
  Widget _buildTypeSpecificContent(String momentType, String typeSpecificData) {
    switch (momentType) {
      case 'fishing_catch':
        final data = MomentTypeDataParser.parseFishingCatch(typeSpecificData);
        if (data == null) return const SizedBox.shrink();
        return _buildFishingCatchHighlight(data);

      case 'equipment':
        final data = MomentTypeDataParser.parseEquipment(typeSpecificData);
        if (data == null) return const SizedBox.shrink();
        return _buildEquipmentHighlight(data);

      case 'technique':
        final data = MomentTypeDataParser.parseTechnique(typeSpecificData);
        if (data == null) return const SizedBox.shrink();
        return _buildTechniqueHighlight(data);

      case 'question':
        final data = MomentTypeDataParser.parseQuestion(typeSpecificData);
        if (data == null) return const SizedBox.shrink();
        return _buildQuestionHighlight(data);

      default:
        return const SizedBox.shrink();
    }
  }

// 钓获分享 - 突出显示最大的鱼和总重量
  Widget _buildFishingCatchHighlight(FishingCatchData data) {
    if (data.caughtFishes == null || data.caughtFishes!.isEmpty) {
      return const SizedBox.shrink();
    }

    final biggestFish = data.caughtFishes!
        .reduce((a, b) => (a.weight ?? 0) > (b.weight ?? 0) ? a : b);

    return Container(
      margin: const EdgeInsets.only(top: 12),
      child: Row(
        children: [
          // 主要渔获展示
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.green.withOpacity(0.08),
                    Colors.green.withOpacity(0.03),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: Colors.green.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  // 鱼图标
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.15),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.set_meal,
                      color: Colors.green.shade700,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          biggestFish.fishTypeName ?? '渔获',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text(
                              '${biggestFish.weight?.toStringAsFixed(1) ?? '0.0'}kg',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.green.shade700,
                              ),
                            ),
                            if (data.caughtFishes!.length > 1) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade600,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '+${data.caughtFishes!.length - 1}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 统计信息
          if (data.totalWeight != null) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    '总重',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.green.shade600,
                    ),
                  ),
                  Text(
                    '${data.totalWeight!.toStringAsFixed(1)}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                  Text(
                    'kg',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.green.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

// 装备展示 - 突出品牌和价格
  Widget _buildEquipmentHighlight(EquipmentData data) {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withOpacity(0.08),
            Colors.blue.withOpacity(0.03),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.blue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 装备图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.backpack,
              color: Colors.blue.shade700,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          // 装备信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    if (data.brand != null) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade600,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          data.brand!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    Expanded(
                      child: Text(
                        data.equipmentName ?? '装备',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                if (data.price != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    data.price!,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

// 技巧分享 - 突出技巧名称和难度
  Widget _buildTechniqueHighlight(TechniqueData data) {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.withOpacity(0.08),
            Colors.orange.withOpacity(0.03),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.orange.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 技巧图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.tips_and_updates,
              color: Colors.orange.shade700,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          // 技巧信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.techniqueName ?? '技巧分享',
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (data.difficulty != null) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      // 难度星级显示
                      ..._buildDifficultyStars(data.difficulty!),
                      const SizedBox(width: 8),
                      Text(
                        data.difficulty!,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.orange.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

// 难度星级
  List<Widget> _buildDifficultyStars(String difficulty) {
    int stars = 0;
    switch (difficulty) {
      case '入门级':
        stars = 1;
        break;
      case '初级':
        stars = 2;
        break;
      case '中级':
        stars = 3;
        break;
      case '高级':
        stars = 4;
        break;
      case '专业级':
        stars = 5;
        break;
    }

    return List.generate(
        5,
        (index) => Icon(
              index < stars ? Icons.star : Icons.star_border,
              size: 14,
              color: Colors.orange.shade600,
            ));
  }

// 问答求助 - 突出问题和标签
  Widget _buildQuestionHighlight(QuestionData data) {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple.withOpacity(0.08),
            Colors.purple.withOpacity(0.03),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.purple.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 问题图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.purple.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.help_outline,
              color: Colors.purple.shade700,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          // 问题信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.questionTitle ?? '求助',
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (data.tags != null && data.tags!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 4,
                    children: data.tags!
                        .take(3)
                        .map((tag) => Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.purple.shade100,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                tag,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.purple.shade700,
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

// 新增：带页面指示器的图片轮播
  Widget _buildImprovedImageCarousel(List<String> images) {
    if (images.isEmpty) return const SizedBox.shrink();

    return Container(
      height: 200,
      child: StatefulBuilder(
        builder: (context, setState) {
          int currentIndex = 0;

          return Stack(
            children: [
              PageView.builder(
                onPageChanged: (index) {
                  setState(() {
                    currentIndex = index;
                  });
                },
                itemCount: images.length,
                itemBuilder: (context, index) {
                  return CachedNetworkImage(
                    imageUrl: images[index],
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey.shade100,
                      child: Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.grey.shade400,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey.shade100,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            color: Colors.grey.shade400,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '图片加载失败',
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),

              // 图片数量和页面指示器
              if (images.length > 1)
                Positioned(
                  bottom: 12,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // 当前页 / 总页数
                            Text(
                              '${currentIndex + 1} / ${images.length}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 12),
                            // 点指示器
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: List.generate(
                                images.length > 5 ? 5 : images.length,
                                // 最多显示5个点
                                (index) {
                                  // 对于超过5张图片的情况，显示省略号
                                  if (images.length > 5 && index == 4) {
                                    return Container(
                                      width: 6,
                                      height: 6,
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 2),
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.white54,
                                      ),
                                    );
                                  }

                                  final actualIndex = images.length > 5
                                      ? (currentIndex < 3
                                          ? index
                                          : currentIndex - 2 + index)
                                      : index;

                                  return Container(
                                    width: currentIndex == actualIndex ? 16 : 6,
                                    height: 6,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 2),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(3),
                                      color: currentIndex == actualIndex
                                          ? Colors.white
                                          : Colors.white.withOpacity(0.4),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Color _getMomentTypeColor(String momentType) {
    switch (momentType) {
      case 'fishing_catch':
        return Colors.green.shade600;
      case 'equipment':
        return Colors.blue.shade600;
      case 'technique':
        return Colors.orange.shade600;
      case 'question':
        return Colors.purple.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  IconData _getMomentTypeIcon(String momentType) {
    switch (momentType) {
      case 'fishing_catch':
        return Icons.set_meal;
      case 'equipment':
        return Icons.backpack;
      case 'technique':
        return Icons.tips_and_updates;
      case 'question':
        return Icons.help_outline;
      default:
        return Icons.article;
    }
  }

  String _formatFlatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }

  void _navigateToMomentDetail(MomentVo moment) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MomentDetailPage(momentId: moment.id!),
      ),
    );
  }
}
